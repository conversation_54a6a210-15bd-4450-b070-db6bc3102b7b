@extends('layouts.app')

@section('title', $lecture->title . ' - Edit Lecture')

@push('styles')
<style>
body {
    background-color: #000;
}

.content-editor {
    min-height: 300px;
}

.lecture-preview {
    background-color: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    padding: 1rem;
}

.resource-item {
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.quiz-question {
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.assignment-rubric {
    background-color: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.375rem;
    padding: 1rem;
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-black text-white">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('instructor.courses.show', $course) }}" 
                           class="text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </a>
                        <div>
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    @if($lecture->type === 'video') bg-red-600 text-white
                                    @elseif($lecture->type === 'text') bg-blue-600 text-white
                                    @elseif($lecture->type === 'quiz') bg-green-600 text-white
                                    @elseif($lecture->type === 'assignment') bg-purple-600 text-white
                                    @else bg-gray-600 text-white @endif">
                                    {{ ucfirst($lecture->type) }}
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $lecture->is_published ? 'bg-green-600 text-white' : 'bg-yellow-600 text-black' }}">
                                    {{ $lecture->is_published ? 'Published' : 'Draft' }}
                                </span>
                                @if($lecture->is_free_preview)
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-600 text-white">Free Preview</span>
                                @endif
                            </div>
                            <h1 class="text-3xl font-bold text-white">{{ $lecture->title }}</h1>
                            <p class="text-gray-400 mt-1">
                                Chapter {{ $chapter->sort_order }}: {{ $chapter->title }} • 
                                Lecture {{ $lecture->sort_order }}
                            </p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <form action="{{ route('instructor.courses.chapters.lectures.toggle-status', [$course, $chapter, $lecture]) }}" method="POST" class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit"
                                    class="bg-{{ $lecture->is_published ? 'yellow' : 'green' }}-600 hover:bg-{{ $lecture->is_published ? 'yellow' : 'green' }}-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-{{ $lecture->is_published ? 'eye-slash' : 'eye' }} mr-2"></i>
                                {{ $lecture->is_published ? 'Unpublish' : 'Publish' }}
                            </button>
                        </form>
                        <a href="{{ route('instructor.courses.chapters.lectures.edit', [$course, $chapter, $lecture]) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-edit mr-2"></i>Edit
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Lecture Content -->
                <div class="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-6">
                    <h2 class="text-xl font-bold text-white mb-4">Lecture Content</h2>
                    
                    @if($lecture->type === 'video')
                        <div class="lecture-preview mb-4">
                            @if($lecture->video_url)
                                <div class="aspect-video bg-black rounded-lg mb-4 flex items-center justify-center">
                                    @if(str_contains($lecture->video_url, 'youtube.com') || str_contains($lecture->video_url, 'youtu.be'))
                                        <iframe src="{{ $lecture->video_url }}" 
                                                class="w-full h-full rounded-lg" 
                                                frameborder="0" 
                                                allowfullscreen></iframe>
                                    @else
                                        <video controls class="w-full h-full rounded-lg">
                                            <source src="{{ $lecture->video_url }}" type="video/mp4">
                                            Your browser does not support the video tag.
                                        </video>
                                    @endif
                                </div>
                            @else
                                <div class="aspect-video bg-gray-800 rounded-lg mb-4 flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-video text-4xl text-gray-500 mb-2"></i>
                                        <p class="text-gray-400">No video uploaded yet</p>
                                    </div>
                                </div>
                            @endif
                            
                            @if($lecture->content)
                                <div class="prose prose-invert max-w-none">
                                    <h4 class="text-white font-medium mb-2">Video Description:</h4>
                                    <div class="text-gray-300">{!! nl2br(e($lecture->content)) !!}</div>
                                </div>
                            @endif
                        </div>
                    
                    @elseif($lecture->type === 'text')
                        <div class="lecture-preview">
                            @if($lecture->content)
                                <div class="prose prose-invert max-w-none">
                                    <div class="text-gray-300 leading-relaxed">{!! nl2br(e($lecture->content)) !!}</div>
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <i class="fas fa-file-text text-4xl text-gray-500 mb-2"></i>
                                    <p class="text-gray-400">No content added yet</p>
                                </div>
                            @endif
                        </div>
                    
                    @elseif($lecture->type === 'quiz')
                        <div class="lecture-preview">
                            @if($lecture->quiz_questions && count($lecture->quiz_questions) > 0)
                                <h4 class="text-white font-medium mb-4">Quiz Questions ({{ count($lecture->quiz_questions) }})</h4>
                                @foreach($lecture->quiz_questions as $index => $question)
                                    <div class="quiz-question">
                                        <h5 class="text-white font-medium mb-2">Question {{ $index + 1 }}</h5>
                                        <p class="text-gray-300 mb-3">{{ $question['question'] }}</p>
                                        
                                        @if($question['type'] === 'multiple_choice')
                                            <div class="space-y-2">
                                                @foreach($question['options'] as $optionIndex => $option)
                                                    <div class="flex items-center space-x-2">
                                                        <span class="w-6 h-6 rounded-full border-2 {{ $optionIndex === $question['correct_answer'] ? 'bg-green-600 border-green-600' : 'border-gray-500' }} flex items-center justify-center">
                                                            @if($optionIndex === $question['correct_answer'])
                                                                <i class="fas fa-check text-white text-xs"></i>
                                                            @endif
                                                        </span>
                                                        <span class="text-gray-300">{{ $option }}</span>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @elseif($question['type'] === 'true_false')
                                            <div class="space-y-2">
                                                <div class="flex items-center space-x-2">
                                                    <span class="w-6 h-6 rounded-full border-2 {{ $question['correct_answer'] === 'true' ? 'bg-green-600 border-green-600' : 'border-gray-500' }} flex items-center justify-center">
                                                        @if($question['correct_answer'] === 'true')
                                                            <i class="fas fa-check text-white text-xs"></i>
                                                        @endif
                                                    </span>
                                                    <span class="text-gray-300">True</span>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="w-6 h-6 rounded-full border-2 {{ $question['correct_answer'] === 'false' ? 'bg-green-600 border-green-600' : 'border-gray-500' }} flex items-center justify-center">
                                                        @if($question['correct_answer'] === 'false')
                                                            <i class="fas fa-check text-white text-xs"></i>
                                                        @endif
                                                    </span>
                                                    <span class="text-gray-300">False</span>
                                                </div>
                                            </div>
                                        @endif
                                        
                                        @if(isset($question['explanation']) && $question['explanation'])
                                            <div class="mt-3 p-3 bg-gray-800 rounded">
                                                <h6 class="text-yellow-400 font-medium text-sm mb-1">Explanation:</h6>
                                                <p class="text-gray-300 text-sm">{{ $question['explanation'] }}</p>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-8">
                                    <i class="fas fa-question-circle text-4xl text-gray-500 mb-2"></i>
                                    <p class="text-gray-400">No quiz questions added yet</p>
                                </div>
                            @endif
                        </div>
                    
                    @elseif($lecture->type === 'assignment')
                        <div class="lecture-preview">
                            @if($lecture->content)
                                <div class="mb-6">
                                    <h4 class="text-white font-medium mb-2">Assignment Instructions:</h4>
                                    <div class="prose prose-invert max-w-none">
                                        <div class="text-gray-300 leading-relaxed">{!! nl2br(e($lecture->content)) !!}</div>
                                    </div>
                                </div>
                            @endif
                            
                            @if($lecture->assignment_details)
                                <div class="assignment-rubric">
                                    <h4 class="text-white font-medium mb-3">Assignment Details:</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        @if(isset($lecture->assignment_details['max_points']))
                                            <div>
                                                <span class="text-gray-400">Max Points:</span>
                                                <span class="text-white ml-2">{{ $lecture->assignment_details['max_points'] }}</span>
                                            </div>
                                        @endif
                                        @if(isset($lecture->assignment_details['due_date']))
                                            <div>
                                                <span class="text-gray-400">Due Date:</span>
                                                <span class="text-white ml-2">{{ $lecture->assignment_details['due_date'] }}</span>
                                            </div>
                                        @endif
                                        @if(isset($lecture->assignment_details['submission_type']))
                                            <div>
                                                <span class="text-gray-400">Submission Type:</span>
                                                <span class="text-white ml-2">{{ ucfirst($lecture->assignment_details['submission_type']) }}</span>
                                            </div>
                                        @endif
                                        @if(isset($lecture->assignment_details['allowed_attempts']))
                                            <div>
                                                <span class="text-gray-400">Allowed Attempts:</span>
                                                <span class="text-white ml-2">{{ $lecture->assignment_details['allowed_attempts'] }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif
                            
                            @if(!$lecture->content && !$lecture->assignment_details)
                                <div class="text-center py-8">
                                    <i class="fas fa-tasks text-4xl text-gray-500 mb-2"></i>
                                    <p class="text-gray-400">No assignment details added yet</p>
                                </div>
                            @endif
                        </div>
                    
                    @elseif($lecture->type === 'resource')
                        <div class="lecture-preview">
                            @php
                                $files = $lecture->resources['files'] ?? $lecture->resources ?? [];
                            @endphp
                            @if($files && count($files) > 0)
                                <h4 class="text-white font-medium mb-4">Resources ({{ count($files) }})</h4>
                                <div class="space-y-3">
                                    @foreach($files as $resource)
                                        <div class="resource-item flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center">
                                                    @php
                                                        $extension = pathinfo($resource['filename'], PATHINFO_EXTENSION);
                                                        $iconClass = match(strtolower($extension)) {
                                                            'pdf' => 'fas fa-file-pdf text-red-400',
                                                            'doc', 'docx' => 'fas fa-file-word text-blue-400',
                                                            'xls', 'xlsx' => 'fas fa-file-excel text-green-400',
                                                            'ppt', 'pptx' => 'fas fa-file-powerpoint text-orange-400',
                                                            'zip', 'rar' => 'fas fa-file-archive text-yellow-400',
                                                            'jpg', 'jpeg', 'png', 'gif' => 'fas fa-file-image text-purple-400',
                                                            'mp4', 'avi', 'mov' => 'fas fa-file-video text-red-400',
                                                            'mp3', 'wav' => 'fas fa-file-audio text-green-400',
                                                            default => 'fas fa-file text-gray-400'
                                                        };
                                                    @endphp
                                                    <i class="{{ $iconClass }}"></i>
                                                </div>
                                                <div>
                                                    <h5 class="text-white font-medium">{{ $resource['original_name'] ?? $resource['filename'] }}</h5>
                                                    <p class="text-gray-400 text-sm">{{ $resource['size'] ?? 'Unknown size' }}</p>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <a href="{{ $resource['url'] }}" target="_blank" 
                                                   class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                                                    <i class="fas fa-external-link-alt mr-1"></i>View
                                                </a>
                                                <a href="{{ $resource['download_url'] ?? $resource['url'] }}" download 
                                                   class="text-green-400 hover:text-green-300 text-sm font-medium">
                                                    <i class="fas fa-download mr-1"></i>Download
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <i class="fas fa-file-download text-4xl text-gray-500 mb-2"></i>
                                    <p class="text-gray-400">No resources uploaded yet</p>
                                </div>
                            @endif
                            
                            @if($lecture->content)
                                <div class="mt-6">
                                    <h4 class="text-white font-medium mb-2">Resource Description:</h4>
                                    <div class="prose prose-invert max-w-none">
                                        <div class="text-gray-300 leading-relaxed">{!! nl2br(e($lecture->content)) !!}</div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Lecture Info -->
                <div class="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Lecture Information</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Type:</span>
                            <span class="text-white">{{ ucfirst($lecture->type) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Status:</span>
                            <span class="text-white">{{ $lecture->is_published ? 'Published' : 'Draft' }}</span>
                        </div>
                        @if($lecture->duration_minutes)
                            <div class="flex justify-between">
                                <span class="text-gray-400">Duration:</span>
                                <span class="text-white">{{ $lecture->duration_minutes }} min</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-400">Order:</span>
                            <span class="text-white">{{ $lecture->sort_order }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Free Preview:</span>
                            <span class="text-white">{{ $lecture->is_free_preview ? 'Yes' : 'No' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Created:</span>
                            <span class="text-white">{{ $lecture->created_at->format('M j, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Updated:</span>
                            <span class="text-white">{{ $lecture->updated_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-gray-900 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{{ route('instructor.courses.chapters.lectures.edit', [$course, $chapter, $lecture]) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>Edit Lecture
                        </a>
                        
                        <form action="{{ route('instructor.courses.chapters.lectures.toggle-status', [$course, $chapter, $lecture]) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <button type="submit" 
                                    class="w-full bg-{{ $lecture->is_published ? 'yellow' : 'green' }}-600 hover:bg-{{ $lecture->is_published ? 'yellow' : 'green' }}-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-{{ $lecture->is_published ? 'eye-slash' : 'eye' }} mr-2"></i>
                                {{ $lecture->is_published ? 'Unpublish' : 'Publish' }}
                            </button>
                        </form>
                        
                        <div class="flex space-x-2">
                            @if($lecture->sort_order > 1)
                                <form action="{{ route('instructor.courses.chapters.lectures.move-up', [$course, $chapter, $lecture]) }}" method="POST" class="flex-1">
                                    @csrf
                                    <button type="submit" 
                                            class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-chevron-up mr-2"></i>Move Up
                                    </button>
                                </form>
                            @endif
                            
                            @if($lecture->sort_order < $chapter->lectures->count())
                                <form action="{{ route('instructor.courses.chapters.lectures.move-down', [$course, $chapter, $lecture]) }}" method="POST" class="flex-1">
                                    @csrf
                                    <button type="submit" 
                                            class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-chevron-down mr-2"></i>Move Down
                                    </button>
                                </form>
                            @endif
                        </div>
                        
                        <form action="{{ route('instructor.courses.chapters.lectures.destroy', [$course, $chapter, $lecture]) }}" 
                              method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this lecture? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-trash mr-2"></i>Delete Lecture
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive functionality here if needed
    console.log('Lecture show page loaded');
});
</script>
@endpush
