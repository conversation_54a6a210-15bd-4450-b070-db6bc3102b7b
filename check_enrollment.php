<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Check enrollment
$courseId = 'c83d1b9e-b26a-4d5e-bd59-f3de0b9e18c4';
$lectureId = '546117c4-2967-405c-8970-4d99bb7d0391';

echo "=== Enrollment Check ===\n";

// Find course
$course = \App\Models\Course::find($courseId);
echo "Course: " . ($course ? $course->title : 'Not found') . "\n";
echo "Course Published: " . ($course && $course->status === 'published' ? 'Yes' : 'No') . "\n";

// Find lecture
$lecture = \App\Models\Lecture::find($lectureId);
echo "Lecture: " . ($lecture ? $lecture->title : 'Not found') . "\n";
echo "Lecture Type: " . ($lecture ? $lecture->type : 'N/A') . "\n";

// Check all enrollments for this course
$enrollments = \App\Models\Enrollment::where('course_id', $courseId)->get();
echo "\nTotal Enrollments: " . $enrollments->count() . "\n";

foreach ($enrollments as $enrollment) {
    $user = $enrollment->user;
    echo "User: " . $user->name . " (ID: " . $user->id . ") - Status: " . $enrollment->status . "\n";
}

// Check if there are any active enrollments
$activeEnrollments = \App\Models\Enrollment::where('course_id', $courseId)
    ->where('status', 'active')
    ->get();
    
echo "\nActive Enrollments: " . $activeEnrollments->count() . "\n";

// Check lecture resources
if ($lecture && $lecture->resources) {
    echo "\nLecture Resources:\n";
    $resources = $lecture->resources;
    
    // Handle both array and JSON string formats
    if (is_string($resources)) {
        $resources = json_decode($resources, true);
    }
    
    if (is_array($resources)) {
        foreach ($resources as $index => $resource) {
            echo "Resource " . ($index + 1) . ":\n";
            echo "  - File Path: " . ($resource['file_path'] ?? 'N/A') . "\n";
            echo "  - Original Name: " . ($resource['original_name'] ?? 'N/A') . "\n";
            echo "  - Download URL: " . ($resource['download_url'] ?? 'N/A') . "\n";
            echo "  - Student Download URL: " . ($resource['student_download_url'] ?? 'N/A') . "\n";
        }
    }
}

// Check if course needs to be published
echo "\n=== ISSUE FOUND ===\n";
if ($course->status !== 'published') {
    echo "❌ COURSE IS NOT PUBLISHED!\n";
    echo "This is why students get 403 errors when trying to access files.\n";
    echo "The canAccessLecture method requires the course to be published for students.\n";
}

echo "\n=== End Check ===\n";