

<?php $__env->startSection('title', $course->title . ' - My Courses'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/course-viewer.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/course-viewer.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Meta tags for JavaScript -->
<meta name="course-id" content="<?php echo e($course->slug); ?>">
<?php if(isset($currentLecture)): ?>
<meta name="current-lecture-id" content="<?php echo e($currentLecture->id); ?>">
<?php endif; ?>

<!-- Course Viewer Container -->
<div class="course-viewer">
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Course Viewer Layout -->
    <div class="course-viewer-container">
        <!-- Course Sidebar -->
        <aside class="course-sidebar" role="complementary" aria-label="Course Navigation">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <button class="sidebar-close-btn" aria-label="Close sidebar">
                    <i class="fas fa-times"></i>
                </button>

                <h1 class="course-title"><?php echo e($course->title); ?></h1>

                <!-- Course Progress -->
                <div class="course-progress">
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-bar" style="width: <?php echo e($enrollment->progress_percentage ?? 0); ?>%"></div>
                    </div>
                    <div class="progress-text">
                        <span class="lecture-count"><?php echo e($enrollment->completed_lectures ?? 0); ?>/<?php echo e($enrollment->total_lectures ?? 0); ?> lessons</span>
                        <span class="progress-percentage" id="progress-percentage"><?php echo e(number_format($enrollment->progress_percentage ?? 0, 1)); ?>%</span>
                    </div>
                </div>
            </div>

            <!-- Curriculum Section -->
            <div class="curriculum-section">
                <h2 class="curriculum-title">
                    <button class="sidebar-hamburger-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-bars curriculum-icon"></i>
                    </button>
                    Course Content
                </h2>

                <!-- Chapters and Lectures -->
                <?php $__currentLoopData = $course->chapters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chapterIndex => $chapter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="chapter <?php echo e(isset($currentLecture) && $chapter->lectures->contains('id', $currentLecture->id) ? 'expanded' : ''); ?>">
                        <div class="chapter-header">
                            <h3 class="chapter-title">
                                <span><?php echo e($chapterIndex + 1); ?>. <?php echo e($chapter->title); ?></span>
                                <i class="fas fa-chevron-down chapter-toggle"></i>
                            </h3>
                            <?php if($chapter->description): ?>
                                <p class="chapter-description"><?php echo e($chapter->description); ?></p>
                            <?php endif; ?>
                        </div>

                        <div class="lectures-list">
                            <?php $__currentLoopData = $chapter->lectures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lectureIndex => $lecture): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCompleted = in_array($lecture->id, $completedLectureIds ?? []);
                                    $isCurrent = isset($currentLecture) && $currentLecture->id === $lecture->id;
                                    $lectureNumber = $lectureIndex + 1;
                                ?>

                                <a href="<?php echo e(route('my-courses.lecture', [$course, $lecture])); ?>"
                                   class="lecture-item <?php echo e($isCurrent ? 'active' : ($isCompleted ? 'completed' : '')); ?>"
                                   data-lecture-id="<?php echo e($lecture->id); ?>"
                                   data-lecture-title="<?php echo e($lecture->title); ?>">

                                    <div class="lecture-status <?php echo e($isCurrent ? 'current' : ($isCompleted ? 'completed' : 'pending')); ?>">
                                        <?php if($isCompleted): ?>
                                            <i class="fas fa-check"></i>
                                        <?php elseif($isCurrent): ?>
                                            <i class="fas fa-play"></i>
                                        <?php else: ?>
                                            <?php echo e($lectureNumber); ?>

                                        <?php endif; ?>
                                    </div>

                                    <div class="lecture-content">
                                        <h4 class="lecture-title"><?php echo e($lecture->title); ?></h4>
                                        <div class="lecture-meta">
                                            <?php if($lecture->duration_minutes): ?>
                                                <span class="lecture-duration">
                                                    <i class="fas fa-clock"></i>
                                                    <?php echo e($lecture->duration_minutes); ?>min
                                                </span>
                                            <?php endif; ?>
                                            <span class="lecture-type">
                                                <?php switch($lecture->type):
                                                    case ('video'): ?>
                                                        <i class="fas fa-play-circle"></i> Video
                                                        <?php break; ?>
                                                    <?php case ('text'): ?>
                                                        <i class="fas fa-file-text"></i> Reading
                                                        <?php break; ?>
                                                    <?php case ('quiz'): ?>
                                                        <i class="fas fa-question-circle"></i> Quiz
                                                        <?php break; ?>
                                                    <?php case ('assignment'): ?>
                                                        <i class="fas fa-tasks"></i> Assignment
                                                        <?php break; ?>
                                                    <?php case ('resource'): ?>
                                                        <i class="fas fa-download"></i> Resource
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <i class="fas fa-file"></i> Content
                                                <?php endswitch; ?>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="course-content" role="main">
            <!-- Content Header -->
            <header class="content-header">
                <div class="header-controls">
                    <button class="sidebar-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-bars"></i>
                    </button>

                    <?php if(isset($currentLecture)): ?>
                        <!-- Navigation Controls -->
                        <nav class="lecture-navigation" aria-label="Lecture navigation">
                            <?php
                                $allLectures = collect();
                                foreach($course->chapters as $chapter) {
                                    $allLectures = $allLectures->merge($chapter->lectures);
                                }
                                $currentIndex = $allLectures->search(function($lecture) use ($currentLecture) {
                                    return $lecture->id === $currentLecture->id;
                                });
                                $prevLecture = $currentIndex > 0 ? $allLectures[$currentIndex - 1] : null;
                                $nextLecture = $currentIndex < $allLectures->count() - 1 ? $allLectures[$currentIndex + 1] : null;
                            ?>

                            <?php if($prevLecture): ?>
                                <a href="<?php echo e(route('my-courses.lecture', [$course, $prevLecture])); ?>" class="nav-btn">
                                    <i class="fas fa-chevron-left"></i>
                                    <span class="hidden sm:inline">Previous</span>
                                </a>
                            <?php endif; ?>

                            <?php if($nextLecture): ?>
                                <a href="<?php echo e(route('my-courses.lecture', [$course, $nextLecture])); ?>" class="nav-btn primary">
                                    <span class="hidden sm:inline">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Lecture Content Area -->
            <div class="lecture-content-area">
                <?php if(isset($currentLecture)): ?>
                    <!-- Lecture Header -->
                    <header class="lecture-header">
                        <h1 class="lecture-title-main"><?php echo e($currentLecture->title); ?></h1>
                        <div class="lecture-meta-main">
                            <?php if($currentLecture->duration_minutes): ?>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span><?php echo e($currentLecture->duration_minutes); ?> minutes</span>
                                </div>
                            <?php endif; ?>
                            <div class="meta-item">
                                <i class="fas fa-<?php echo e($currentLecture->type === 'video' ? 'play-circle' : ($currentLecture->type === 'text' ? 'file-text' : 'file')); ?>"></i>
                                <span><?php echo e(ucfirst($currentLecture->type)); ?></span>
                            </div>
                            <?php if($currentLecture->is_free): ?>
                                <div class="meta-item">
                                    <i class="fas fa-unlock"></i>
                                    <span>Free Preview</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </header>

                    <!-- Lecture Content -->
                    <div class="content-wrapper">
                        <?php switch($currentLecture->type):
                            case ('video'): ?>
                                <div class="video-container">
                                    <?php if($currentLecture->video_url): ?>
                                        <?php if(str_contains($currentLecture->video_url, 'youtube.com') || str_contains($currentLecture->video_url, 'youtu.be')): ?>
                            <?php
                                // Convert YouTube watch URL to embed URL
                                $embedUrl = $currentLecture->video_url;
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $currentLecture->video_url, $matches)) {
                                    $videoId = $matches[1];
                                    $embedUrl = "https://www.youtube.com/embed/{$videoId}";
                                }
                            ?>
                            <div class="video-embed">
                                <iframe src="<?php echo e($embedUrl); ?>"
                                        title="<?php echo e($currentLecture->title); ?>"
                                        frameborder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                        allowfullscreen>
                                </iframe>
                            </div>
                                        <?php else: ?>
                                            <video class="video-player" controls>
                                                <source src="<?php echo e($currentLecture->video_url); ?>" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="empty-state">
                                            <div class="empty-state-icon">🎥</div>
                                            <h3>Video Coming Soon</h3>
                                            <p>The video content for this lecture will be available soon.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php break; ?>

                            <?php case ('text'): ?>
                                <div class="text-content">
                                    <?php echo $currentLecture->content ?? '<p>Content coming soon...</p>'; ?>

                                </div>
                                <?php break; ?>

                            <?php case ('quiz'): ?>
                                <div class="quiz-content">
                                    <h3>Quiz: <?php echo e($currentLecture->title); ?></h3>
                                    <?php if($currentLecture->content): ?>
                                        <?php echo $currentLecture->content; ?>

                                    <?php else: ?>
                                        <p>Quiz content will be available soon.</p>
                                    <?php endif; ?>
                                </div>
                                <?php break; ?>

                            <?php case ('assignment'): ?>
                                <div class="assignment-content">
                                    <h3>Assignment: <?php echo e($currentLecture->title); ?></h3>
                                    <?php if($currentLecture->content): ?>
                                        <?php echo $currentLecture->content; ?>

                                    <?php else: ?>
                                        <p>Assignment details will be available soon.</p>
                                    <?php endif; ?>
                                </div>
                                <?php break; ?>

                            <?php case ('resource'): ?>
                                <div class="resource-content">
                                    <h3>Resource: <?php echo e($currentLecture->title); ?></h3>
                                    
                                    <?php if($currentLecture->content): ?>
                                        <div class="resource-description">
                                            <?php echo $currentLecture->content; ?>

                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php
                                        // Handle both old and new data structures
                                        $files = [];
                                        if ($currentLecture->resources && is_array($currentLecture->resources)) {
                                            if (isset($currentLecture->resources['files'])) {
                                                // New structure: files are under 'files' key
                                                $files = $currentLecture->resources['files'];
                                            } else {
                                                // Old structure: files are directly in resources array
                                                // Check if it's an array of files (has 'name' key in first element)
                                                if (count($currentLecture->resources) > 0 && isset($currentLecture->resources[0]['name'])) {
                                                    $files = $currentLecture->resources;
                                                }
                                            }
                                        }
                                    ?>

                                    <?php if(count($files) > 0): ?>
                        <div class="resource-files mt-4">
                            <h4 class="text-lg font-semibold mb-3">📁 Downloadable Files</h4>
                            <div class="files-list space-y-2">
                                <?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="file-item bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                                                        <div class="file-info flex items-center">
                                                            <div class="file-icon mr-3">
                                                                <?php
                                                                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                                                                    $iconClass = match(strtolower($extension)) {
                                                                        'pdf' => 'fas fa-file-pdf text-red-500',
                                                                        'doc', 'docx' => 'fas fa-file-word text-blue-500',
                                                                        'xls', 'xlsx' => 'fas fa-file-excel text-green-500',
                                                                        'ppt', 'pptx' => 'fas fa-file-powerpoint text-orange-500',
                                                                        'zip', 'rar' => 'fas fa-file-archive text-purple-500',
                                                                        'jpg', 'jpeg', 'png', 'gif' => 'fas fa-file-image text-pink-500',
                                                                        default => 'fas fa-file text-gray-500'
                                                                    };
                                                                ?>
                                                                <i class="<?php echo e($iconClass); ?> text-2xl"></i>
                                                            </div>
                                                            <div class="file-details">
                                                                <div class="file-name font-medium text-gray-900"><?php echo e($file['name']); ?></div>
                                                                <div class="file-meta text-sm text-gray-500">
                                                                    <?php echo e(number_format(($file['file_size'] ?? $file['size'] ?? 0) / 1024, 1)); ?> KB
                                                                    <?php if(isset($file['uploaded_at'])): ?>
                                                                        • Uploaded <?php echo e(\Carbon\Carbon::parse($file['uploaded_at'])->diffForHumans()); ?>

                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="file-actions">
                                                            <?php if(isset($file['file_path'])): ?>
                                                                
                                                                <a href="<?php echo e(route('files.download-lecture-resource', [$course->id, $currentLecture->id, basename($file['file_path'])])); ?>"
                                                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center"
                                                                   download>
                                                                    <i class="fas fa-download mr-2"></i>
                                                                    Download
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php elseif($currentLecture->resources && is_array($currentLecture->resources) && isset($currentLecture->resources['url']) && $currentLecture->resources['url']): ?>
                                        <div class="resource-url mt-4">
                                            <h4 class="text-lg font-semibold mb-3">🔗 External Resource</h4>
                                            <a href="<?php echo e($currentLecture->resources['url']); ?>" 
                                               target="_blank" 
                                               class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                                                <i class="fas fa-external-link-alt mr-2"></i>
                                                Access Resource
                                            </a>
                                        </div>
                                    <?php elseif(!$currentLecture->content): ?>
                                        <div class="empty-state">
                                            <div class="empty-state-icon">📄</div>
                                            <h3>Resource Coming Soon</h3>
                                            <p>Resource will be available for download soon.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php break; ?>

                            <?php default: ?>
                                <div class="default-content">
                                    <?php if($currentLecture->content): ?>
                                        <?php echo $currentLecture->content; ?>

                                    <?php else: ?>
                                        <div class="empty-state">
                                            <div class="empty-state-icon">📄</div>
                                            <h3>Content Coming Soon</h3>
                                            <p>The content for this lecture will be available soon.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                        <?php endswitch; ?>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="navigation-buttons">
                                <?php if($prevLecture): ?>
                                    <a href="<?php echo e(route('my-courses.lecture', [$course, $prevLecture])); ?>" class="nav-btn">
                                        <i class="fas fa-chevron-left"></i>
                                        <span>Previous Lesson</span>
                                    </a>
                                <?php endif; ?>

                                <?php if($nextLecture): ?>
                                    <a href="<?php echo e(route('my-courses.lecture', [$course, $nextLecture])); ?>" class="nav-btn primary">
                                        <span>Next Lesson</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>

                            <!-- Mark Complete Button -->
                            <?php if(!in_array($currentLecture->id, $completedLectureIds ?? [])): ?>
                                <button class="complete-btn" data-lecture-id="<?php echo e($currentLecture->id); ?>">
                                    <i class="fas fa-check"></i>
                                    <span>Mark as Complete</span>
                                </button>
                            <?php else: ?>
                                <button class="complete-btn completed" disabled>
                                    <i class="fas fa-check"></i>
                                    <span>Completed</span>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Empty State - No Lecture Selected -->
                    <div class="empty-state">
                        <div class="empty-state-icon">🎓</div>
                        <h3>Ready to Start Learning?</h3>
                        <p>Select a lesson from the sidebar to begin your journey, or jump right into the first lesson.</p>
                        <?php if($course->chapters->isNotEmpty() && $course->chapters->first()->lectures->isNotEmpty()): ?>
                            <a href="<?php echo e(route('my-courses.lecture', [$course, $course->chapters->first()->lectures->first()])); ?>"
                               class="complete-btn">
                                Start First Lesson
                                <i class="fas fa-play"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/enrollments/view-course.blade.php ENDPATH**/ ?>