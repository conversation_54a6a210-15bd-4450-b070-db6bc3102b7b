<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LectureController extends Controller
{
    /**
     * Display a listing of lectures for a chapter.
     */
    public function index(Course $course, Chapter $chapter)
    {
        $this->authorize('view', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $lectures = $chapter->lectures()
            ->orderBy('sort_order')
            ->get();

        return view('instructor.lectures.index', compact('course', 'chapter', 'lectures'));
    }

    /**
     * Show the form for creating a new lecture.
     */
    public function create(Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $lectureTypes = [
            'video' => 'Video Lecture',
            'text' => 'Text Content',
            'quiz' => 'Quiz',
            'assignment' => 'Assignment',
            'resource' => 'Resource/Download',
        ];

        return view('instructor.lectures.create', compact('course', 'chapter', 'lectureTypes'));
    }

    /**
     * Store a newly created lecture in storage.
     */
    public function store(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        try {
            $this->validateLecture($request);

            $data = $request->all();
            $data['chapter_id'] = $chapter->id;
            $data['course_id'] = $course->id;
            $data['instructor_id'] = $course->instructor_id;
            $data['slug'] = $this->generateUniqueSlug($request->title, $chapter->id);
            $data['sort_order'] = $chapter->lectures()->count() + 1;
            $data['is_published'] = false;

            // Handle boolean fields properly for AJAX requests
            $data['is_free_preview'] = $this->parseBooleanValue($request->is_free_preview ?? false);
            $data['is_mandatory'] = $this->parseBooleanValue($request->is_mandatory ?? true);

            // Handle numeric fields that cannot be null in database
            $numericFields = ['duration_minutes', 'quiz_passing_score', 'estimated_completion_minutes'];
            foreach ($numericFields as $field) {
                if (array_key_exists($field, $data)) {
                    // Convert null/empty values to 0 for database compatibility
                    if (is_null($data[$field]) || $data[$field] === '') {
                        $data[$field] = 0;
                    }
                }
            }

            // Handle video metadata
            if ($request->type === 'video' && $request->video_url) {
                $data['video_metadata'] = $this->extractVideoMetadata($request->video_url);
            }

            // Handle file uploads for resources
            if ($request->hasFile('resource_files')) {
                $data['resources'] = $this->storeResourceFiles($request->file('resource_files'), $course->id);
            }

            $lecture = Lecture::create($data);

            // Return JSON response for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Lecture created successfully!',
                    'lecture' => $lecture
                ]);
            }

            return redirect()->route('instructor.courses.chapters.lectures.show', [$course, $chapter, $lecture])
                ->with('success', 'Lecture created successfully!');

        } catch (\Exception $e) {
            // Return JSON response for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create lecture: ' . $e->getMessage()
                ], 422);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create lecture: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified lecture.
     */
    public function show(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('view', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        return view('instructor.lectures.show', compact('course', 'chapter', 'lecture'));
    }

    /**
     * Show the form for editing the specified lecture.
     */
    public function edit(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        $lectureTypes = [
            'video' => 'Video Lecture',
            'text' => 'Text Content',
            'quiz' => 'Quiz',
            'assignment' => 'Assignment',
            'resource' => 'Resource/Download',
        ];

        return view('instructor.lectures.edit', compact('course', 'chapter', 'lecture', 'lectureTypes'));
    }

    /**
     * Update the specified lecture in storage.
     */
    public function update(Request $request, Course $course, Chapter $chapter, Lecture $lecture)
    {
        try {
            \Log::info('Lecture update started', [
                'lecture_id' => $lecture->id,
                'request_data' => $request->all(),
                'is_ajax' => $request->ajax(),
                'headers' => $request->headers->all()
            ]);

            $this->authorize('update', $course);
            $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

            // Check for concurrent edits if this is an auto-save
            if ($request->input('auto_save') === '1' && $request->has('last_updated')) {
                $lastUpdated = \Carbon\Carbon::parse($request->input('last_updated'));
                $lectureUpdated = \Carbon\Carbon::parse($lecture->updated_at);

                if ($lectureUpdated->gt($lastUpdated)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'This lecture has been modified by another user. Please refresh the page.',
                        'conflict' => true
                    ], 409);
                }
            }

            $this->validateLecture($request);

            $data = $request->all();

            // Handle boolean fields properly for AJAX requests
            $data['is_free_preview'] = $this->parseBooleanValue($request->is_free_preview ?? $lecture->is_free_preview);
            $data['is_mandatory'] = $this->parseBooleanValue($request->is_mandatory ?? $lecture->is_mandatory);

            // Handle numeric fields that cannot be null in database
            $numericFields = ['duration_minutes', 'quiz_passing_score', 'estimated_completion_minutes'];
            foreach ($numericFields as $field) {
                if (array_key_exists($field, $data)) {
                    // Convert null/empty values to 0 for database compatibility
                    if (is_null($data[$field]) || $data[$field] === '') {
                        $data[$field] = 0;
                    }
                }
            }

            // Update slug if title changed
            if ($request->title !== $lecture->title) {
                $data['slug'] = $this->generateUniqueSlug($request->title, $chapter->id, $lecture->id);
            }

            // Handle video metadata
            if ($request->type === 'video' && $request->video_url) {
                $data['video_metadata'] = $this->extractVideoMetadata($request->video_url);
            }

            // Handle file uploads for resources
            if ($request->hasFile('resource_files')) {
                // Delete old resource files
                if ($lecture->resources) {
                    // Handle both data structures: direct array or nested under 'files' key
                    $files = $lecture->resources['files'] ?? $lecture->resources ?? [];
                    foreach ($files as $resource) {
                        if (isset($resource['file_path'])) {
                            Storage::disk('private')->delete($resource['file_path']);
                        }
                    }
                }
                $data['resources'] = $this->storeResourceFiles($request->file('resource_files'), $course->id);
            }

            $lecture->update($data);

            \Log::info('Lecture updated successfully', [
                'lecture_id' => $lecture->id,
                'is_ajax' => request()->ajax()
            ]);

            // Check if this is an AJAX request
            if (request()->ajax()) {
                $response = [
                    'success' => true,
                    'message' => 'Lecture updated successfully!',
                    'lecture' => $lecture
                ];

                \Log::info('Returning AJAX response', $response);

                return response()->json($response, 200, [
                    'Content-Type' => 'application/json',
                    'Cache-Control' => 'no-cache, must-revalidate'
                ]);
            }

            return redirect()->route('instructor.courses.show', $course)
                ->with('success', 'Lecture updated successfully!');

        } catch (\Illuminate\Validation\ValidationException $e) {
            if (request()->ajax()) {
                // Check if this is an auto-save request
                $isAutoSave = $request->input('auto_save') === '1';

                return response()->json([
                    'success' => false,
                    'message' => $isAutoSave ? 'Please fix validation errors' : 'Validation failed',
                    'validation_errors' => true,
                    'errors' => $e->errors()
                ], 422, [
                    'Content-Type' => 'application/json'
                ]);
            }
            throw $e;
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to perform this action'
                ], 403, [
                    'Content-Type' => 'application/json'
                ]);
            }
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Lecture update failed: ' . $e->getMessage(), [
                'lecture_id' => $lecture->id,
                'chapter_id' => $chapter->id,
                'course_id' => $course->id,
                'user_id' => auth()->id()
            ]);

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An unexpected error occurred. Please try again.'
                ], 500, [
                    'Content-Type' => 'application/json'
                ]);
            }

            return redirect()->route('instructor.courses.show', $course)
                ->with('error', 'An unexpected error occurred. Please try again.');
        }
    }

    /**
     * Remove the specified lecture from storage.
     */
    public function destroy(Request $request, Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        // Delete resource files
        if ($lecture->resources) {
            // Handle both data structures: direct array or nested under 'files' key
            $files = $lecture->resources['files'] ?? $lecture->resources ?? [];
            foreach ($files as $resource) {
                if (isset($resource['file_path'])) {
                    Storage::disk('private')->delete($resource['file_path']);
                }
            }
        }

        $lecture->delete();

        $successMessage = 'Lecture deleted successfully.';

        // Return JSON response for AJAX requests
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => $successMessage
            ]);
        }

        return redirect()->route('instructor.courses.show', $course)
            ->with('success', $successMessage);
    }

    /**
     * Toggle lecture published status.
     */
    public function toggleStatus(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        $lecture->update(['is_published' => !$lecture->is_published]);

        $message = $lecture->is_published ? 'Lecture published successfully!' : 'Lecture unpublished successfully.';

        return redirect()->back()->with('success', $message);
    }

    /**
     * Move lecture up in sort order.
     */
    public function moveUp(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        if ($lecture->moveUp()) {
            return redirect()->back()->with('success', 'Lecture moved up successfully.');
        }

        return redirect()->back()->with('error', 'Cannot move lecture up. It\'s already at the top.');
    }

    /**
     * Move lecture down in sort order.
     */
    public function moveDown(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        if ($lecture->moveDown()) {
            return redirect()->back()->with('success', 'Lecture moved down successfully.');
        }

        return redirect()->back()->with('error', 'Cannot move lecture down. It\'s already at the bottom.');
    }

    /**
     * Duplicate a lecture.
     */
    public function duplicate(Course $course, Chapter $chapter, Lecture $lecture)
    {
        $this->authorize('update', $course);
        $this->ensureLectureBelongsToChapter($lecture, $chapter, $course);

        $newLecture = $lecture->replicate();
        $newLecture->title = $lecture->title . ' (Copy)';
        $newLecture->slug = $this->generateUniqueSlug($newLecture->title, $chapter->id);
        $newLecture->is_published = false;
        $newLecture->sort_order = Lecture::where('chapter_id', $chapter->id)->max('sort_order') + 1;
        $newLecture->save();

        return redirect()->route('instructor.courses.chapters.lectures.show', [$course, $chapter, $newLecture])
            ->with('success', 'Lecture duplicated successfully! You can now edit the copy.');
    }

    /**
     * Bulk update lecture order.
     */
    public function updateOrder(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $request->validate([
            'lectures' => 'required|array',
            'lectures.*' => 'exists:lectures,id',
        ]);

        foreach ($request->lectures as $index => $lectureId) {
            Lecture::where('id', $lectureId)
                ->where('chapter_id', $chapter->id)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true, 'message' => 'Lecture order updated successfully.']);
    }

    /**
     * Bulk delete lectures.
     */
    public function bulkDelete(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $request->validate([
            'lecture_ids' => 'required|string',
        ]);

        $lectureIds = explode(',', $request->lecture_ids);

        $deletedCount = Lecture::whereIn('id', $lectureIds)
            ->where('chapter_id', $chapter->id)
            ->delete();

        return redirect()->back()
            ->with('success', "Successfully deleted {$deletedCount} lectures.");
    }

    /**
     * Bulk publish lectures.
     */
    public function bulkPublish(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $request->validate([
            'lecture_ids' => 'required|string',
        ]);

        $lectureIds = explode(',', $request->lecture_ids);

        $updatedCount = Lecture::whereIn('id', $lectureIds)
            ->where('chapter_id', $chapter->id)
            ->update(['is_published' => true]);

        return redirect()->back()
            ->with('success', "Successfully published {$updatedCount} lectures.");
    }

    /**
     * Bulk unpublish lectures.
     */
    public function bulkUnpublish(Request $request, Course $course, Chapter $chapter)
    {
        $this->authorize('update', $course);
        $this->ensureChapterBelongsToCourse($chapter, $course);

        $request->validate([
            'lecture_ids' => 'required|string',
        ]);

        $lectureIds = explode(',', $request->lecture_ids);

        $updatedCount = Lecture::whereIn('id', $lectureIds)
            ->where('chapter_id', $chapter->id)
            ->update(['is_published' => false]);

        return redirect()->back()
            ->with('success', "Successfully unpublished {$updatedCount} lectures.");
    }

    /**
     * Validate lecture data based on type.
     */
    private function validateLecture(Request $request)
    {
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'type' => 'required|in:video,text,quiz,assignment,resource',
            'duration_minutes' => 'nullable|integer|min:0|max:1440',
            'is_free_preview' => 'boolean',
            'is_mandatory' => 'boolean',
        ];

        // Type-specific validation - make them optional for basic creation
        switch ($request->type) {
            case 'video':
                $rules['video_url'] = 'nullable|url';
                $rules['video_provider'] = 'nullable|in:youtube,vimeo,self_hosted';
                break;
            case 'text':
                $rules['content'] = 'nullable|string';
                $rules['estimated_completion_minutes'] = 'nullable|integer|min:0|max:1440';
                break;
            case 'quiz':
                $rules['quiz_data'] = 'nullable|array';
                $rules['quiz_passing_score'] = 'nullable|integer|min:0|max:100';
                $rules['quiz_allow_retakes'] = 'boolean';
                break;
            case 'assignment':
                $rules['content'] = 'nullable|string';
                $rules['estimated_completion_minutes'] = 'nullable|integer|min:0|max:1440';
                break;
            case 'resource':
                $rules['content'] = 'nullable|string';
                $rules['resource_files.*'] = 'file|max:10240'; // 10MB max per file
                break;
        }

        $request->validate($rules);
    }

    /**
     * Generate a unique slug for the lecture within the chapter.
     */
    private function generateUniqueSlug(string $title, string $chapterId, ?string $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = Lecture::where('chapter_id', $chapterId)->where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Extract video metadata from URL.
     */
    private function extractVideoMetadata(string $videoUrl): array
    {
        $metadata = [
            'url' => $videoUrl,
            'provider' => 'unknown',
            'video_id' => null,
            'thumbnail' => null,
        ];

        // YouTube
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $videoUrl, $matches)) {
            $metadata['provider'] = 'youtube';
            $metadata['video_id'] = $matches[1];
            $metadata['thumbnail'] = "https://img.youtube.com/vi/{$matches[1]}/maxresdefault.jpg";
        }
        // Vimeo
        elseif (preg_match('/vimeo\.com\/(\d+)/', $videoUrl, $matches)) {
            $metadata['provider'] = 'vimeo';
            $metadata['video_id'] = $matches[1];
        }

        return $metadata;
    }

    /**
     * Store resource files for lecture.
     */
    private function storeResourceFiles(array $files, string $courseId): array
    {
        $resources = [];
        $directory = "courses/" . auth()->id() . "/{$courseId}/resources";

        foreach ($files as $file) {
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs($directory, $filename, 'private');

            $resources[] = [
                'name' => $file->getClientOriginalName(),
                'file_path' => $path,
                'file_size' => $file->getSize(),
                'file_type' => $file->getClientMimeType(),
                'uploaded_at' => now()->toISOString(),
            ];
        }

        return $resources;
    }

    /**
     * Ensure chapter belongs to the course.
     */
    private function ensureChapterBelongsToCourse(Chapter $chapter, Course $course): void
    {
        if ($chapter->course_id !== $course->id) {
            abort(404, 'Chapter not found in this course.');
        }
    }

    /**
     * Parse boolean value from various input formats.
     */
    private function parseBooleanValue($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['1', 'true', 'on', 'yes']);
        }

        return (bool) $value;
    }

    /**
     * Ensure lecture belongs to the chapter and course.
     */
    private function ensureLectureBelongsToChapter(Lecture $lecture, Chapter $chapter, Course $course): void
    {
        if ($lecture->chapter_id !== $chapter->id || $lecture->course_id !== $course->id) {
            abort(404, 'Lecture not found in this chapter.');
        }
    }
}
